#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终图表修复验证测试
验证中英文图表都能正确生成，字体显示正常，连线正确
"""

import sys
sys.path.append('.')
from AFLDE_IM import *
import networkx as nx
import matplotlib.pyplot as plt

def test_font_availability():
    """测试系统可用字体"""
    print("=" * 60)
    print("系统字体检测")
    print("=" * 60)
    
    import matplotlib.font_manager as fm
    
    # 检查中文字体
    chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    print("检测中文字体支持:")
    for font in chinese_fonts:
        status = "✅ 可用" if font in available_fonts else "❌ 不可用"
        print(f"  {font}: {status}")
    
    print(f"\n系统总共有 {len(available_fonts)} 个字体")
    print("推荐字体:", [f for f in chinese_fonts if f in available_fonts][:3])

def test_simple_chart():
    """测试简单图表生成"""
    print("\n" + "=" * 60)
    print("简单图表测试")
    print("=" * 60)
    
    # 创建测试数据
    x = range(10)
    y1 = [i**0.5 for i in x]
    y2 = [i*1.2 for i in x]
    
    # 测试英文图表
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(x, y1, 'o-', label='English Test')
    plt.xlabel('Generation')
    plt.ylabel('Value')
    plt.title('English Chart Test')
    plt.legend()
    plt.grid(True)
    
    # 测试中文图表
    plt.subplot(1, 2, 2)
    plt.plot(x, y2, 's-', label='中文测试', color='red')
    plt.xlabel('代数')
    plt.ylabel('数值')
    plt.title('中文图表测试')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('simple_chart_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 简单图表测试完成: simple_chart_test.png")

def test_algorithm_charts():
    """测试完整算法图表生成"""
    print("\n" + "=" * 60)
    print("完整算法图表测试")
    print("=" * 60)
    
    # 创建小测试网络
    G = nx.erdos_renyi_graph(40, 0.15)
    isolates = list(nx.isolates(G))
    G.remove_nodes_from(isolates)
    
    k = 4
    p = 0.05
    pop = 8
    g = 6
    FEsMaxs = 150
    SN = 15
    
    print(f'测试参数: 节点数={G.number_of_nodes()}, k={k}, pop={pop}, g={g}')
    
    try:
        start_time = time.time()
        final_population, final_fitness = ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN)
        end_time = time.time()
        
        print('✅ 算法运行成功!')
        print(f'运行时间: {end_time - start_time:.2f} 秒')
        print(f'最终适应度: {max(final_fitness):.4f}')
        print('图表生成完成!')
        
        return True
        
    except Exception as e:
        print(f'❌ 运行失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def check_generated_files():
    """检查生成的文件"""
    print("\n" + "=" * 60)
    print("生成文件检查")
    print("=" * 60)
    
    import os
    import glob
    
    # 查找生成的图表文件
    patterns = [
        "*_ENGLISH.png", "*_ENGLISH.svg", "*_ENGLISH.pdf",
        "*_CHINESE.png", "*_CHINESE.svg", "*_CHINESE.pdf",
        "simple_chart_test.png"
    ]
    
    all_files = []
    for pattern in patterns:
        files = glob.glob(pattern)
        all_files.extend(files)
    
    if all_files:
        print("✅ 找到以下生成的图表文件:")
        for file in sorted(all_files):
            size = os.path.getsize(file) / 1024  # KB
            print(f"  📊 {file} ({size:.1f} KB)")
    else:
        print("❌ 未找到生成的图表文件")
    
    return all_files

def create_comparison_summary():
    """创建对比总结图"""
    print("\n" + "=" * 60)
    print("创建对比总结")
    print("=" * 60)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 模拟数据
    x = range(8)
    lambda_data = [0.2, 0.4, 0.6, 0.8, 0.7, 0.5, 0.3, 0.6]
    fitness_data = [15, 18, 22, 25, 27, 28, 29, 30]
    mutation_data = [2, 2, 1, 0, 3, 2, 1, 0]
    
    # 英文版本
    ax1.plot(x, lambda_data, 'o-', color='blue', linewidth=2)
    ax1.set_title('Lambda Value (English)', fontweight='bold')
    ax1.set_xlabel('Generation')
    ax1.set_ylabel('Lambda Value')
    ax1.grid(True, alpha=0.3)
    
    ax2.plot(x, fitness_data, 's-', color='purple', linewidth=2)
    ax2.set_title('Fitness Value (English)', fontweight='bold')
    ax2.set_xlabel('Generation')
    ax2.set_ylabel('Fitness Value')
    ax2.grid(True, alpha=0.3)
    
    # 中文版本
    ax3.plot(x, lambda_data, 'o-', color='blue', linewidth=2)
    ax3.set_title('λ值变化 (中文)', fontweight='bold')
    ax3.set_xlabel('代数')
    ax3.set_ylabel('λ值')
    ax3.grid(True, alpha=0.3)
    
    # 变异算子状态图
    colors = ['#F18F01', '#C73E1D', '#2E86AB', '#A23B72']
    ax4.plot(x, mutation_data, 'o-', color='#2E86AB', linewidth=2, markersize=8)
    for i, (xi, yi) in enumerate(zip(x, mutation_data)):
        ax4.scatter(xi, yi, color=colors[yi], s=100, zorder=3, edgecolors='white', linewidth=2)
    
    ax4.set_title('变异算子状态 (连线修复)', fontweight='bold')
    ax4.set_xlabel('代数')
    ax4.set_ylabel('状态')
    ax4.set_yticks([0, 1, 2, 3])
    ax4.set_yticklabels(['收敛', '开发', '探索', '逃逸'])
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('chart_comparison_summary.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 对比总结图已保存: chart_comparison_summary.png")

def main():
    """主测试函数"""
    print("ANFDE-IM 最终图表修复验证")
    print("测试目标:")
    print("1. 验证中文字体正确显示")
    print("2. 验证变异算子状态图正确连线")
    print("3. 验证中英文双版本图表生成")
    print("4. 验证矢量图格式输出")
    
    # 测试1: 字体检测
    test_font_availability()
    
    # 测试2: 简单图表
    test_simple_chart()
    
    # 测试3: 完整算法
    success = test_algorithm_charts()
    
    # 测试4: 文件检查
    files = check_generated_files()
    
    # 测试5: 对比总结
    create_comparison_summary()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success and files:
        print("🎉 所有测试通过!")
        print("✅ 中文字体显示问题已解决")
        print("✅ 变异算子状态图连线问题已解决")
        print("✅ 中英文双版本图表生成正常")
        print("✅ 矢量图格式输出正常")
        print(f"✅ 共生成 {len(files)} 个图表文件")
        
        print("\n📊 生成的图表类型:")
        print("- 英文版本: 完全避免字体问题")
        print("- 中文版本: 尝试使用系统中文字体")
        print("- 多格式: PNG/SVG/PDF 三种格式")
        print("- 连线修复: 变异算子状态正确连接")
        
    else:
        print("❌ 部分测试失败")
        if not success:
            print("- 算法运行失败")
        if not files:
            print("- 图表文件生成失败")
    
    print("\n推荐使用:")
    print("- 如果中文显示正常: 使用中文版本图表")
    print("- 如果中文显示异常: 使用英文版本图表")
    print("- 论文发表: 推荐使用PDF矢量图")
    print("- 演示展示: 推荐使用PNG高清图")

if __name__ == "__main__":
    main()
