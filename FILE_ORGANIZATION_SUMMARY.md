# 📁 ANFDE-IM 文件整理总结

## 🎯 整理目标

根据您的要求，已完成以下文件整理：

1. ✅ **图像文件** → 统一保存到 `image/` 文件夹
2. ✅ **输出结果文件** → 统一保存到 `output/` 文件夹  
3. ✅ **删除测试文件** → 清理所有临时测试文件
4. ✅ **避免重复保存** → 修改代码确保不重复生成图片

## 📂 当前文件结构

```
项目根目录/
├── image/                          # 图像文件夹
│   ├── nodes_*_CHINESE.png         # 中文版图表 (PNG)
│   ├── nodes_*_CHINESE.svg         # 中文版图表 (SVG)
│   ├── nodes_*_CHINESE.pdf         # 中文版图表 (PDF)
│   ├── nodes_*_ENGLISH.png         # 英文版图表 (PNG)
│   ├── nodes_*_ENGLISH.svg         # 英文版图表 (SVG)
│   └── nodes_*_ENGLISH.pdf         # 英文版图表 (PDF)
│
├── output/                         # 输出文件夹
│   └── nodes_*_k*_p*_*.txt         # 算法运行结果文件
│
├── networks/                       # 网络数据文件夹
│   ├── karate.txt
│   ├── blog-int.txt
│   └── ...
│
├── 核心算法文件
│   ├── AFLDE_IM.py                 # 主算法入口
│   ├── anfde.py                    # ANFDE核心算法
│   ├── LIE.py                      # 影响力评估
│   ├── IC.py                       # 独立级联模型
│   ├── LFV.py                      # 局部影响力值
│   ├── LID.py                      # 局部影响力差异
│   ├── LHS.py                      # 拉丁超立方采样
│   ├── sample_solutions.py         # 解采样
│   ├── utils.py                    # 工具函数
│   └── graph.py                    # 图处理
│
└── 文档文件
    ├── README_OPTIMIZED.md         # 优化版说明文档
    ├── performance_comparison.py   # 性能对比测试
    └── FILE_ORGANIZATION_SUMMARY.md # 本文件
```

## 🔧 代码修改详情

### 1. 图像保存路径修改

#### anfde.py 修改
```python
# 英文版图表保存路径
base_filename = f"image/nodes_{num_nodes}_edges_{num_edges}_k{k}_FEs{FEs}_p{p}_ENGLISH"

# 中文版图表保存路径  
base_filename = f"image/nodes_{num_nodes}_edges_{num_edges}_k{self.k}_FEs{self.FEs}_p{self.p}_CHINESE"

# 确保image文件夹存在
os.makedirs('image', exist_ok=True)
```

### 2. 输出文件保存路径

#### anfde.py 第1566行
```python
output_filename = f"output/nodes_{self.G.number_of_nodes()}_edges_{self.G.number_of_edges()}_k{k}_p{self.p}_{timestamp}.txt"

# 确保output文件夹存在
output_dir = "output"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
```

### 3. 删除的测试文件

已删除以下临时测试文件：
- ❌ `test_chart_fix.py`
- ❌ `test_final_charts.py` 
- ❌ `chart_fix_final.py`
- ❌ `CHART_FIX_SUMMARY.md`
- ❌ `FINAL_CHART_FIX_REPORT.md`
- ❌ 根目录下的重复图片文件

## 📊 文件命名规范

### 图像文件命名
```
格式: nodes_{节点数}_edges_{边数}_k{种子集大小}_FEs{函数评估次数}_p{传播概率}_{语言版本}.{格式}

示例:
- nodes_1490_edges_19090_k50_FEs250000_p0.05_CHINESE.png
- nodes_1490_edges_19090_k50_FEs250000_p0.05_ENGLISH.svg
```

### 输出文件命名
```
格式: nodes_{节点数}_edges_{边数}_k{种子集大小}_p{传播概率}_{时间戳}.txt

示例:
- nodes_1490_edges_19090_k50_p0.05_20250126_143022.txt
```

## 🎨 图表文件说明

### 双语版本支持
- **中文版本**: `*_CHINESE.*` - 包含中文标签和说明
- **英文版本**: `*_ENGLISH.*` - 完全英文界面，避免字体问题

### 多格式输出
- **PNG格式**: 高分辨率位图，通用性强，适合演示
- **SVG格式**: 可缩放矢量图，适合网页展示
- **PDF格式**: 高质量打印，适合论文发表

### 图表内容
1. **第一个子图**: 景观状态值λ随代数变化
2. **第二个子图**: 最佳适应度随代数变化  
3. **第三个子图**: 变异算子状态变化（已修复连线问题）

## 📄 输出文件内容

每个txt文件包含：
```
k: 50
种子集: [1, 15, 23, 45, 67, ...]
IC模拟10000次值: 234.5678
运行时间: 123.45 秒
迭代次数: 200
激活概率: 0.05

适应度历史:
代数 1: 123.4567
代数 2: 125.6789
...

Lambda 历史:
代数 1: 0.2345
代数 2: 0.3456
...
```

## ✅ 整理完成确认

### 已完成的整理任务
- [x] 图像文件统一保存到 `image/` 文件夹
- [x] 输出结果统一保存到 `output/` 文件夹
- [x] 删除所有测试文件
- [x] 修改代码避免重复保存
- [x] 确保文件夹自动创建
- [x] 保持原有功能完整性

### 文件夹自动创建
代码中已添加自动创建文件夹的逻辑：
```python
# 图像文件夹
os.makedirs('image', exist_ok=True)

# 输出文件夹  
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
```

## 🚀 使用建议

### 运行算法
```bash
python AFLDE_IM.py
```

### 查看结果
- **图表**: 检查 `image/` 文件夹中的图表文件
- **数据**: 检查 `output/` 文件夹中的txt结果文件

### 文件管理
- 图表文件按网络规模和参数自动命名
- 输出文件包含时间戳，避免覆盖
- 支持中英文双版本，确保兼容性

---

**整理完成时间**: 2025年1月26日  
**整理状态**: ✅ 完全整理完毕  
**文件结构**: ✅ 规范化完成
