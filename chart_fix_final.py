#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表显示问题最终解决方案
提供中文和英文两个版本的图表生成函数
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np

def create_english_chart(anfde_instance, filename=None, save_vector=True):
    """生成英文版本的图表，避免中文字体问题"""
    
    # 使用英文标签
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 10
    
    num_nodes = anfde_instance.G.number_of_nodes()
    num_edges = anfde_instance.G.number_of_edges()
    
    if filename is None:
        base_filename = f"nodes_{num_nodes}_edges_{num_edges}_k{anfde_instance.k}_FEs{anfde_instance.FEs}_p{anfde_instance.p}_EN"
    else:
        base_filename = filename.rsplit('.', 1)[0] + "_EN"
    
    # 确保历史数据长度一致
    min_length = min(len(anfde_instance.lambda_history), 
                    len(anfde_instance.fitness_history), 
                    len(anfde_instance.mutation_history))
    generations = range(min_length)
    
    # 创建图表
    plt.figure(figsize=(18, 6), dpi=300)
    
    # 绘制λ值
    plt.subplot(1, 3, 1)
    plt.plot(generations, anfde_instance.lambda_history[:min_length], 
            label='Lambda Value', color='#2E86AB', linewidth=2, marker='o', markersize=3)
    plt.xlabel('Generation', fontsize=12)
    plt.ylabel('Lambda Value', fontsize=12)
    plt.title('Landscape State Value λ vs Generation', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 绘制适应度
    plt.subplot(1, 3, 2)
    plt.plot(generations, anfde_instance.fitness_history[:min_length], 
            label='Fitness', color='#A23B72', linewidth=2, marker='s', markersize=3)
    plt.xlabel('Generation', fontsize=12)
    plt.ylabel('Fitness Value', fontsize=12)
    plt.title('Best Fitness vs Generation', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 绘制变异算子状态
    plt.subplot(1, 3, 3)
    state_to_num = {
        'convergence': 0,
        'exploitation': 1,
        'exploration': 2,
        'escape': 3
    }
    
    state_colors = {
        0: '#F18F01',  # convergence - orange
        1: '#C73E1D',  # exploitation - red
        2: '#2E86AB',  # exploration - blue
        3: '#A23B72'   # escape - purple
    }
    
    mutation_history_truncated = anfde_instance.mutation_history[:min_length]
    mutation_states_numeric = [state_to_num.get(state, 0) for state in mutation_history_truncated]
    
    if len(mutation_states_numeric) > 0:
        # 绘制连续的状态线
        plt.plot(generations, mutation_states_numeric, 
                linewidth=2, marker='o', markersize=6, 
                color='#2E86AB', alpha=0.8, label='State Change')
        
        # 为每个状态点添加颜色标记
        for i, (gen, state) in enumerate(zip(generations, mutation_states_numeric)):
            color = state_colors.get(state, '#000000')
            plt.scatter(gen, state, color=color, s=50, zorder=3, 
                       edgecolors='white', linewidth=1)
        
        # 添加状态区域背景色
        for i in range(4):
            plt.axhspan(i-0.4, i+0.4, alpha=0.1, color=state_colors[i])
    
    plt.xlabel('Generation', fontsize=12)
    plt.ylabel('Mutation Operator State', fontsize=12)
    plt.title('Mutation Operator State Changes', fontsize=14, fontweight='bold')
    plt.yticks([0, 1, 2, 3], ['Convergence', 'Exploitation', 'Exploration', 'Escape'])
    plt.ylim(-0.5, 3.5)
    plt.grid(True, linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    formats = []
    if save_vector:
        svg_filename = f"{base_filename}.svg"
        pdf_filename = f"{base_filename}.pdf"
        plt.savefig(svg_filename, format='svg', dpi=300, bbox_inches='tight')
        plt.savefig(pdf_filename, format='pdf', dpi=300, bbox_inches='tight')
        formats.extend([svg_filename, pdf_filename])
        print(f"English vector charts saved: {svg_filename}, {pdf_filename}")
    
    png_filename = f"{base_filename}.png"
    plt.savefig(png_filename, format='png', dpi=300, bbox_inches='tight')
    formats.append(png_filename)
    print(f"English bitmap saved: {png_filename}")
    
    plt.close()
    return formats

def test_chart_comparison():
    """测试中英文图表对比"""
    print("=" * 60)
    print("图表显示问题解决方案测试")
    print("=" * 60)
    
    # 创建模拟数据
    class MockANFDE:
        def __init__(self):
            self.k = 5
            self.FEs = 100
            self.p = 0.05
            self.lambda_history = [0.1, 0.3, 0.5, 0.7, 0.6, 0.4, 0.2, 0.8, 0.9, 0.5]
            self.fitness_history = [10.5, 12.3, 15.1, 18.7, 20.2, 22.1, 23.5, 24.8, 25.2, 25.5]
            self.mutation_history = ['exploration', 'exploration', 'exploitation', 'convergence', 
                                   'escape', 'exploration', 'exploitation', 'convergence', 
                                   'convergence', 'exploration']
            
            # 模拟图对象
            class MockGraph:
                def number_of_nodes(self):
                    return 50
                def number_of_edges(self):
                    return 120
            
            self.G = MockGraph()
    
    # 创建模拟实例
    mock_anfde = MockANFDE()
    
    # 生成英文版图表
    print("\n生成英文版图表...")
    english_files = create_english_chart(mock_anfde, save_vector=True)
    
    print("\n✅ 图表生成完成!")
    print("生成的文件:")
    for file in english_files:
        print(f"  - {file}")
    
    print("\n📊 解决方案说明:")
    print("1. 英文版图表完全避免中文字体问题")
    print("2. 保持所有原有功能和美观度")
    print("3. 支持SVG/PDF/PNG多格式输出")
    print("4. 变异算子状态图正确连线显示")
    
    return english_files

if __name__ == "__main__":
    test_chart_comparison()
