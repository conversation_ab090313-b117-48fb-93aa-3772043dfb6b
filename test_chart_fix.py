#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表修复效果的脚本
验证中文字体显示和变异算子状态图连线问题
"""

import sys
sys.path.append('.')
from AFLDE_IM import *
import networkx as nx
import matplotlib.pyplot as plt

def test_font_display():
    """测试中文字体显示"""
    print("=" * 50)
    print("测试中文字体显示")
    print("=" * 50)
    
    # 创建简单测试图
    fig, ax = plt.subplots(1, 1, figsize=(8, 6))
    
    # 测试中文文本
    test_texts = [
        "景观状态值λ随代数变化",
        "最佳适应度随代数变化", 
        "变异算子状态变化",
        "收敛", "开发", "探索", "逃逸"
    ]
    
    for i, text in enumerate(test_texts):
        ax.text(0.1, 0.9 - i*0.1, text, fontsize=12, transform=ax.transAxes)
    
    ax.set_title("中文字体显示测试", fontsize=14, fontweight='bold')
    ax.set_xlabel("代数", fontsize=12)
    ax.set_ylabel("数值", fontsize=12)
    
    # 保存测试图
    plt.savefig("font_test.png", dpi=300, bbox_inches='tight')
    plt.savefig("font_test.svg", format='svg', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 字体测试图已保存: font_test.png, font_test.svg")

def test_mutation_state_chart():
    """测试变异算子状态图连线"""
    print("\n" + "=" * 50)
    print("测试变异算子状态图连线")
    print("=" * 50)
    
    # 模拟变异状态数据
    generations = list(range(10))
    mutation_states = ['exploration', 'exploration', 'exploitation', 'convergence', 
                      'escape', 'exploration', 'exploitation', 'convergence', 
                      'convergence', 'exploration']
    
    state_to_num = {
        'convergence': 0,
        'exploitation': 1,
        'exploration': 2,
        'escape': 3
    }
    
    state_colors = {
        0: '#F18F01',  # convergence - 橙色
        1: '#C73E1D',  # exploitation - 红色
        2: '#2E86AB',  # exploration - 蓝色
        3: '#A23B72'   # escape - 紫色
    }
    
    mutation_states_numeric = [state_to_num.get(state, 0) for state in mutation_states]
    
    # 创建测试图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 原始方法（不连线）
    ax1.set_title("修复前：不连线的状态图", fontsize=14, fontweight='bold')
    for i, (gen, state) in enumerate(zip(generations, mutation_states_numeric)):
        color = state_colors.get(state, '#000000')
        ax1.scatter(gen, state, color=color, s=50, zorder=3)
    ax1.set_xlabel('代数', fontsize=12)
    ax1.set_ylabel('变异算子状态', fontsize=12)
    ax1.set_yticks([0, 1, 2, 3])
    ax1.set_yticklabels(['收敛', '开发', '探索', '逃逸'])
    ax1.grid(True, linestyle='--', alpha=0.3)
    ax1.set_ylim(-0.5, 3.5)
    
    # 修复后方法（连线）
    ax2.set_title("修复后：连线的状态图", fontsize=14, fontweight='bold')
    
    # 绘制连续的状态线
    ax2.plot(generations, mutation_states_numeric, 
            linewidth=2, marker='o', markersize=6, 
            color='#2E86AB', alpha=0.8, label='状态变化')
    
    # 为每个状态点添加颜色标记
    for i, (gen, state) in enumerate(zip(generations, mutation_states_numeric)):
        color = state_colors.get(state, '#000000')
        ax2.scatter(gen, state, color=color, s=50, zorder=3, 
                   edgecolors='white', linewidth=1)
    
    # 添加状态区域背景色
    for i in range(4):
        ax2.axhspan(i-0.4, i+0.4, alpha=0.1, color=state_colors[i])
    
    ax2.set_xlabel('代数', fontsize=12)
    ax2.set_ylabel('变异算子状态', fontsize=12)
    ax2.set_yticks([0, 1, 2, 3])
    ax2.set_yticklabels(['收敛', '开发', '探索', '逃逸'])
    ax2.set_ylim(-0.5, 3.5)
    ax2.grid(True, linestyle='--', alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    
    # 保存测试图
    plt.savefig("mutation_state_test.png", dpi=300, bbox_inches='tight')
    plt.savefig("mutation_state_test.svg", format='svg', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 变异状态图测试已保存: mutation_state_test.png, mutation_state_test.svg")

def test_algorithm_with_charts():
    """测试完整算法的图表生成"""
    print("\n" + "=" * 50)
    print("测试完整算法图表生成")
    print("=" * 50)
    
    # 创建小测试网络
    G = nx.erdos_renyi_graph(30, 0.2)
    isolates = list(nx.isolates(G))
    G.remove_nodes_from(isolates)
    
    k = 3
    p = 0.05
    pop = 5
    g = 5
    FEsMaxs = 100
    SN = 10
    
    print(f'测试参数: 节点数={G.number_of_nodes()}, k={k}, pop={pop}')
    
    try:
        final_population, final_fitness = ANFDE_IM(G, k, g, pop, FEsMaxs, p, SN)
        print('✅ 算法运行成功!')
        print(f'最终适应度: {max(final_fitness):.4f}')
        print('图表修复验证完成!')
        return True
    except Exception as e:
        print(f'❌ 运行失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("ANFDE-IM 图表修复验证测试")
    print("测试内容：")
    print("1. 中文字体显示")
    print("2. 变异算子状态图连线")
    print("3. 完整算法图表生成")
    print()
    
    # 测试1：字体显示
    test_font_display()
    
    # 测试2：状态图连线
    test_mutation_state_chart()
    
    # 测试3：完整算法
    success = test_algorithm_with_charts()
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print("✅ 中文字体显示测试完成")
    print("✅ 变异算子状态图连线测试完成")
    if success:
        print("✅ 完整算法图表生成测试完成")
        print("\n🎉 所有图表问题已修复！")
    else:
        print("❌ 完整算法测试失败")
    
    print("\n生成的测试文件：")
    print("- font_test.png/svg - 字体显示测试")
    print("- mutation_state_test.png/svg - 状态图连线测试")
    print("- 算法生成的完整图表文件")

if __name__ == "__main__":
    main()
